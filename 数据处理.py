import os
import re
from datetime import datetime
from typing import List, Dict, Optional, Any, Union
import json
import time

class 数据处理器:
    def __init__(self):
        self.输出目录 = os.path.dirname(os.path.abspath(__file__))
        self.卖家数据目录 = self.输出目录
        self.新上架目录 = self.输出目录
        # 避免循环导入
        import importlib
        配置管理器模块 = importlib.import_module('配置管理')
        self.配置 = 配置管理器模块.配置管理器()
        
    def 设置输出目录(self, 卖家数据目录: str, 新上架目录: str) -> None:
        """设置输出目录"""
        self.卖家数据目录 = 卖家数据目录
        self.新上架目录 = 新上架目录
    
    def 保存数据(self, 数据: List[Dict], 卖家ID: str) -> tuple:
        """保存采集到的数据到文件，如果文件已存在则更新，返回文件路径和新上架商品列表"""
        卖家名称 = self.配置.获取卖家名称(卖家ID)
        
        文件名 = f"卖家_{卖家ID}.json"
        文件路径 = os.path.join(self.卖家数据目录, 文件名)
        当前时间 = datetime.now().strftime("%Y年%m月%d日%H时%M分%S秒")
        
        # 用于存储新上架的商品
        新上架商品 = []
        
        # 创建或更新JSON数据结构
        if os.path.exists(文件路径):
            # 如果文件已存在，读取现有数据
            try:
                with open(文件路径, 'r', encoding='utf-8') as f:
                    现有数据 = json.load(f)
                
                # 更新采集记录
                if "采集记录" not in 现有数据:
                    现有数据["采集记录"] = []
                
                现有数据["采集记录"].append({
                    "采集时间": 当前时间,
                    "商品数量": len(数据)
                })
                
                # 获取新采集的商品ID集合
                新商品ID集合 = {商品.get('id', '未知ID') for 商品 in 数据}
                
                # 获取现有商品ID集合
                现有商品ID集合 = {商品.get("商品ID", "未知ID") for 商品 in 现有数据.get("商品列表", [])}
                
                # 更新商品列表，只保留新采集中存在的商品（未下架的商品）
                现有商品 = {}
                下架商品数量 = 0
                
                for 商品 in 现有数据.get("商品列表", []):
                    商品ID = 商品.get("商品ID", "未知ID")
                    if 商品ID in 新商品ID集合:
                        # 商品未下架，保留
                        现有商品[商品ID] = 商品
                    else:
                        # 商品已下架，不保留
                        下架商品数量 += 1
                
                print(f"检测到 {下架商品数量} 个商品已下架，已从数据中删除")
                
                # 处理新采集的商品
                for 商品 in 数据:
                    商品ID = 商品.get('id', '未知ID')
                    商品标题 = 商品.get('title', '未知标题')
                    
                    # 检查是否为新上架商品
                    if 商品ID not in 现有商品ID集合:
                        新商品数据 = {
                            "商品ID": 商品ID,
                            "商品标题": 商品标题
                        }
                        # 添加想要人数信息（如果有）
                        if 'want_count' in 商品:
                            新商品数据["想要人数"] = 商品['want_count']
                            
                        新上架商品.append(新商品数据)
                    
                    商品数据 = {
                        "商品ID": 商品ID,
                        "商品标题": 商品标题,
                        "首次采集时间": 现有商品.get(商品ID, {}).get("首次采集时间", 当前时间),
                        "最近采集时间": 当前时间
                    }
                    
                    # 保留原有游戏名称（如果有）
                    if 商品ID in 现有商品 and "游戏名称" in 现有商品[商品ID]:
                        商品数据["游戏名称"] = 现有商品[商品ID]["游戏名称"]
                    
                    # 如果有想要人数，添加到数据中
                    if '想要人数' in 商品 or 'want_count' in 商品:
                        商品数据["想要人数"] = 商品.get('want_count', 商品.get('想要人数', 0))
                    
                    现有商品[商品ID] = 商品数据
                
                print(f"检测到 {len(新上架商品)} 个新上架商品")
                
                # 更新基本信息
                现有数据["卖家信息"]["最近采集时间"] = 当前时间
                现有数据["卖家信息"]["总采集次数"] = len(现有数据["采集记录"])
                现有数据["卖家信息"]["卖家名称"] = 卖家名称
                现有数据["商品列表"] = list(现有商品.values())
                
                json数据 = 现有数据
                
            except Exception as e:
                print(f"读取现有数据失败: {e}，将创建新文件")
                # 如果读取失败，创建新的数据结构
                json数据 = self._创建新数据结构(卖家ID, 卖家名称, 数据, 当前时间)
                # 所有商品都视为新上架
                for 商品 in 数据:
                    新商品数据 = {
                        "商品ID": 商品.get('id', '未知ID'),
                        "商品标题": 商品.get('title', '未知标题')
                    }
                    # 添加想要人数信息（如果有）
                    if 'want_count' in 商品:
                        新商品数据["想要人数"] = 商品['want_count']
                        
                    新上架商品.append(新商品数据)
        else:
            # 如果文件不存在，创建新的数据结构
            json数据 = self._创建新数据结构(卖家ID, 卖家名称, 数据, 当前时间)
            # 所有商品都视为新上架
            for 商品 in 数据:
                新商品数据 = {
                    "商品ID": 商品.get('id', '未知ID'),
                    "商品标题": 商品.get('title', '未知标题')
                }
                # 添加想要人数信息（如果有）
                if 'want_count' in 商品:
                    新商品数据["想要人数"] = 商品['want_count']
                    
                新上架商品.append(新商品数据)
        
        # 保存JSON文件
        with open(文件路径, 'w', encoding='utf-8') as f:
            json.dump(json数据, f, ensure_ascii=False, indent=2)
        
        # 保存新上架商品标题到txt文件
        self.保存新上架商品(新上架商品)
        
        return (文件路径, 新上架商品)
    
    def 保存新上架商品(self, 新上架商品: List[Dict]) -> None:
        """保存新上架商品标题到txt文件（追加模式）"""
        if not 新上架商品:
            return
            
        文件名 = "新上架游戏.txt"
        文件路径 = os.path.join(self.新上架目录, 文件名)
        
        try:
            with open(文件路径, 'a', encoding='utf-8') as f:
                for 商品 in 新上架商品:
                    想要人数 = 商品.get("想要人数", 0)
                    f.write(f"{商品['商品标题']} [想要人数: {想要人数}]\n")
            print(f"已将 {len(新上架商品)} 个新上架商品标题保存到 {文件名}")
        except Exception as e:
            print(f"保存新上架商品标题时出错: {e}")
    
    def _创建新数据结构(self, 卖家ID: str, 卖家名称: str, 数据: List[Dict], 当前时间: str) -> Dict:
        """创建新的数据结构"""
        json数据 = {
            "卖家信息": {
                "卖家ID": 卖家ID,
                "卖家名称": 卖家名称,
                "首次采集时间": 当前时间,
                "最近采集时间": 当前时间,
                "总采集次数": 1
            },
            "采集记录": [
                {
                    "采集时间": 当前时间,
                    "商品数量": len(数据)
                }
            ],
            "商品列表": []
        }
        
        # 处理商品数据
        for 商品 in 数据:
            商品数据 = {
                "商品ID": 商品.get('id', '未知ID'),
                "商品标题": 商品.get('title', '未知标题'),
                "首次采集时间": 当前时间,
                "最近采集时间": 当前时间
            }
            
            # 如果有想要人数，添加到数据中
            if '想要人数' in 商品 or 'want_count' in 商品:
                商品数据["想要人数"] = 商品.get('want_count', 商品.get('想要人数', 0))
            
            json数据["商品列表"].append(商品数据)
        
        return json数据
    
    def 加载数据(self, 文件路径: str) -> Dict:
        """从文件加载数据"""
        try:
            with open(文件路径, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载数据失败: {e}")
            return {"错误": f"加载数据失败: {e}"}
    
    def 获取所有数据文件(self) -> List[str]:
        """获取目录中所有JSON数据文件"""
        文件列表 = []
        for 文件名 in os.listdir(self.卖家数据目录):
            if 文件名.startswith("卖家_") and 文件名.endswith(".json"):
                文件列表.append(os.path.join(self.卖家数据目录, 文件名))
        return 文件列表
    
    def 按卖家分组数据(self) -> Dict[str, Dict]:
        """将数据文件按卖家ID分组，并返回卖家信息"""
        卖家分组 = {}
        
        for 文件路径 in self.获取所有数据文件():
            try:
                数据 = self.加载数据(文件路径)
                卖家ID = 数据.get("卖家信息", {}).get("卖家ID", "未知卖家")
                卖家名称 = 数据.get("卖家信息", {}).get("卖家名称", self.配置.获取卖家名称(卖家ID))
                
                卖家分组[卖家ID] = {
                    "文件路径": 文件路径,
                    "卖家ID": 卖家ID,
                    "卖家名称": 卖家名称,
                    "最近采集时间": 数据.get("卖家信息", {}).get("最近采集时间", "未知"),
                    "总采集次数": 数据.get("卖家信息", {}).get("总采集次数", 0),
                    "商品数量": len(数据.get("商品列表", []))
                }
            except:
                continue
                
        return 卖家分组

    def 合并卖家数据(self, 卖家ID: str, 文件列表: List[str]) -> Dict:
        """合并同一卖家的多个数据文件"""
        合并数据 = {
            "卖家信息": {
                "卖家ID": 卖家ID,
                "数据文件数量": len(文件列表)
            },
            "采集记录": [],
            "商品汇总": {}
        }
        
        所有商品 = {}
        
        for 文件路径 in 文件列表:
            数据 = self.加载数据(文件路径)
            
            采集记录 = {
                "采集时间": 数据.get("卖家信息", {}).get("采集时间", "未知时间"),
                "商品数量": len(数据.get("商品列表", []))
            }
            合并数据["采集记录"].append(采集记录)
            
            # 汇总所有商品
            for 商品 in 数据.get("商品列表", []):
                商品ID = 商品.get("商品ID", "未知ID")
                if 商品ID not in 所有商品:
                    所有商品[商品ID] = 商品
                else:
                    # 如果存在多次采集，保留最新的想要人数
                    if "想要人数" in 商品:
                        所有商品[商品ID]["想要人数"] = 商品["想要人数"]
        
        合并数据["商品汇总"] = list(所有商品.values())
        return 合并数据 