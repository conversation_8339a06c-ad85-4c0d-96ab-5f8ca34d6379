import requests
import json
import time
import hashlib
import re
import random
from typing import Dict, List, Union, Tuple, Any
from .配置管理 import 配置管理器
from .Cookie管理 import Cookie管理器

class 数据采集器:
    def __init__(self):
        self.配置 = 配置管理器()
        self.cookie管理 = Cookie管理器()
        self.系统设置 = self.配置.获取系统设置()
        self.用户代理 = self.系统设置["用户代理"]
        self.应用密钥 = self.系统设置["应用密钥"]
        self.接口名称 = self.系统设置["接口名称"]
        self.基础URL = self.系统设置["基础URL"]
        self.cookie = self.cookie管理.获取Cookie()
        
    def 设置Cookie管理器(self, cookie管理器: Cookie管理器) -> None:
        """设置Cookie管理器"""
        self.cookie管理 = cookie管理器
        self.cookie = self.cookie管理.获取Cookie()
        
    def 获取签名(self, token: str, t: int, app_key: str, data: str) -> str:
        return hashlib.md5(f"{token}&{t}&{app_key}&{data}".encode()).hexdigest()
        
    def 清理文本(self, text: Any) -> str:
        if not isinstance(text, str):
            return str(text)
        return text.replace('\n', ' ').replace('\r', '')
        
    def 解析想要人数(self, card_data: Dict) -> Tuple[bool, int]:
        try:
            label_data = card_data.get('itemLabelDataVO', {}).get('labelData', {})
            r3_data = label_data.get('r3', {})
            tag_list = r3_data.get('tagList', [])
            if tag_list and len(tag_list) > 0:
                content = tag_list[0].get('data', {}).get('content', '0人想要')
                if '想要' in content:
                    num = re.search(r'(\d+)', content)
                    if num:
                        return True, int(num.group(1))
            return False, 0
        except:
            return False, 0
            
    def 爬取卖家页面(self, 页码: int, 卖家ID: str, 分组ID: int = 51959993, 分组名称: str = "综合", 重试: bool = True) -> Union[List[Dict], str]:
        print(f"正在爬取卖家 {卖家ID} 的第{页码}页...")
        
        data_params = {
            "pageNumber": 页码,
            "pageSize": 20,
            "userId": 卖家ID,
            "groupId": 分组ID,
            "groupName": 分组名称,
            "needGroupInfo": False,
            "defaultGroup": True,
            "nextPageModel": "recommend"
        }
        
        data_str = json.dumps(data_params, separators=(',', ':'))
        t = int(time.time() * 1000)
        
        try:
            token = self.cookie.split("_m_h5_tk=")[1].split("_")[0]
        except (IndexError, ValueError):
            print("Cookie格式不正确，无法提取token")
            if 重试:
                new_cookie = self.cookie管理.刷新Cookie()
                if new_cookie:
                    self.cookie = new_cookie
                    # 无需等待，立即重试
                    return self.爬取卖家页面(页码, 卖家ID, 分组ID, 分组名称, 重试=False)
            return "COOKIE_ERROR"
        
        signature = self.获取签名(token, t, self.应用密钥, data_str)
        
        params = {
            'jsv': '2.7.2',
            'appKey': self.应用密钥,
            't': t,
            'sign': signature,
            'v': '1.0',
            'api': self.接口名称,
            'type': 'originaljson',
            'dataType': 'json',
            'data': data_str,
            'accountSite': 'xianyu',
            'timeout': '20000'
        }
        
        headers = {
            'cookie': self.cookie,
            'user-agent': self.用户代理
        }
        
        results = []
        
        try:
            response = requests.get(self.基础URL, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            if 'ret' in data:
                if not any("SUCCESS" in ret_str for ret_str in data['ret']):
                    error_msg = f"API调用失败: {data['ret']}"
                    
                    if any("令牌过期" in ret_str for ret_str in data['ret']) or any("TOKEN_EMPTY" in ret_str for ret_str in data['ret']):
                        error_msg = "Cookie已过期，请更新Cookie"
                        print(f"错误: {error_msg}")
                        
                        if 重试:
                            new_cookie = self.cookie管理.刷新Cookie()
                            if new_cookie:
                                self.cookie = new_cookie
                                # 无需等待，立即重试
                                return self.爬取卖家页面(页码, 卖家ID, 分组ID, 分组名称, 重试=False)
                        return "COOKIE_EXPIRED"
                        
                    elif any("签名错误" in ret_str for ret_str in data['ret']):
                        error_msg = "签名计算出错，请检查签名算法"
                    elif any("调用次数超限" in ret_str for ret_str in data['ret']):
                        error_msg = "请求频率过高，被限制"
                    elif any("最大可查看页数或者每页最大可查看商品数超限" in ret_str for ret_str in data['ret']):
                        error_msg = "已到达最大可查看页数限制"
                        print(f"提示: {error_msg}")
                        return "LIMIT_REACHED"
                    
                    print(f"错误: {error_msg}")
                    if 重试:
                        print("尝试重新获取Cookie...")
                        new_cookie = self.cookie管理.刷新Cookie()
                        if new_cookie:
                            self.cookie = new_cookie
                            # 无需等待，立即重试
                            return self.爬取卖家页面(页码, 卖家ID, 分组ID, 分组名称, 重试=False)
                    return "API_ERROR"
            
            if 'data' not in data or not data['data']:
                print("没有更多数据了")
                return "NO_MORE_DATA"
                
            if 'cardList' not in data['data']:
                print("没有更多数据了")
                return "NO_MORE_DATA"
                
            if not data['data']['cardList']:
                print("没有更多数据了")
                return "NO_MORE_DATA"
            
            for card in data['data']['cardList']:
                try:
                    card_data = card.get('cardData', {})
                    title = self.清理文本(card_data.get('title', '未知标题'))
                    item_id = card_data.get('id', '未知ID')
                    has_want, want_count = self.解析想要人数(card_data)
                    
                    if has_want:
                        result = {
                            'title': title,
                            'id': item_id,
                            'want_count': want_count
                        }
                    else:
                        result = {
                            'title': title,
                            'id': item_id
                        }
                    results.append(result)
                except Exception:
                    continue
            
            print(f"第{页码}页成功获取到{len(results)}条商品信息")
            return results
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            if 重试:
                print("尝试重新获取Cookie...")
                new_cookie = self.cookie管理.刷新Cookie()
                if new_cookie:
                    self.cookie = new_cookie
                    # 无需等待，立即重试
                    return self.爬取卖家页面(页码, 卖家ID, 分组ID, 分组名称, 重试=False)
            return "REQUEST_ERROR"
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return "JSON_ERROR"
        except Exception as e:
            print(f"发生未知错误: {e}")
            return "UNKNOWN_ERROR" 